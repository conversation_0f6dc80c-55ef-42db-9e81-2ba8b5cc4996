<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>甜润夏日客官来碗茶</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <!-- 首页 -->
        <van-swipe vertical  v-if="page===1" class="swipe_container" :show-indicators="false" :loop="false">
            <van-swipe-item>
                <div class="page fc" :class="{blur:show}">
                    <div class="rotation animate__animated animate__fadeIn">
                        <van-swipe ref="swipe" class="my-swipe" :autoplay="2000" :show-indicators="false" indicator-color="white" vertical>
                            <van-swipe-item v-for="(item,index) in handleRotation">
                                <p>{{item[0]}}<span>{{item[1]}}</span>{{item[2]}}</p>
                            </van-swipe-item>
                        </van-swipe>
                    </div>
                    <img class="title animate__animated animate__zoomIn" src="img/title.png">
                    <div class="start"></div>
                    <div class="button_container">
                        <img src="img/button2.png" class="button animate__animated animate__fadeInLeft" @click="page=2">
                        <img src="img/button3.png" class="button animate__animated animate__fadeInRight" @click="page=3">
                    </div>
                </div>
            </van-swipe-item>
            <van-swipe-item>
                <div class="page bj2 fc" :class="{blur:show}">
                    <div class="tip">
                        <img src="img/button5.png" class="button5" @click="start">
                    </div>
                </div>
            </van-swipe-item>

        </van-swipe>
        <!-- 活动规则页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===2">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area">
                <img src="img/stit.png" class="stit">
                <div class="rule" v-html="startData.rule"></div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 我的奖品页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===3">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area">
                <img src="img/stit2.png" class="stit">
                <div class="prize">
                    <div class="info">
                        <img src="img/jptit1.png" class="jptit mt5">
                        <p class="p2 p3 mt5" v-if="startData.prize">
                            {{startData.prizeTime}}：{{startData.ad2.replaceAll("\n", "")}}</p>
                        <div class="p2" v-if="startData.prize">{{startData.prize}}</div>
                        <p class="p2 p1 mt5" v-else>暂未中奖</p>
                    </div>
                    <div class="info">
                        <img src="img/jptit2.png" class="jptit">
                        <p class="p2 mt5">{{startData.userInfo.name}}&nbsp;&nbsp;{{startData.userInfo.phone}}</p>
                        <p class="p2">{{startData.userInfo.area.split(',').join('')}}</p>
                        <p class="p2">{{startData.userInfo.address}}</p>
                    </div>
                    <img src="img/edit.png" class="edit" @click="edit">
                </div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 登记信息页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===4">
            <div class="area area2">
                <form class="form">
                    <img src="img/stit3.png" class="stit">
                    <div class="form-item">
                        <label>姓　　名:</label>
                        <input type="text" v-model="form.name">
                    </div>
                    <div class="form-item">
                        <label>联系方式:</label>
                        <input type="number" v-model="form.phone">
                    </div>
                    <div class="form-item fs">
                        <label>邮寄地址:</label>
                        <div class="right" @click="focus">
                            <input type="text" placeholder="选择省" v-model="form.area.split(',')[0]" readonly>
                            <input type="text" placeholder="选择市" v-model="form.area.split(',')[1]" readonly>
                            <input type="text" placeholder="选择区" v-model="form.area.split(',')[2]" readonly>
                        </div>
                    </div>
                    <div class="form-item">
                        <label>街道地址:</label>
                        <input type="text" v-model="form.address" @keyup.enter="submit">
                    </div>
                    <div class="form-footer">
                        <p class="fz1">◎</p>
                        <p>免责声明:<br>本活动专题收集的所有信息仅供发放活动奖品使用，在未经得本人同意情况下绝对不会将您的任何资料以任何方式泄露给第三方。由于您自身原因如共享登录账号等导致的个人信息披露，活动方概不负责。
                        </p>
                    </div>
                    <img src="img/button6.png" class="back animate__animated animate__fadeInUp" @click="submit">
                </form>
                <van-popup v-model:show="popupShow" round position="bottom">
                    <van-picker show-toolbar title="请选择地区" :columns="options" default-index="11"
                        v-model="selectedValues" @cancel="popupShow = false" @confirm="onConfirm"></van-picker>
                </van-popup>
            </div>
        </div>
        <!-- 游戏页 -->
        <div class="page bj3 fc" :class="{blur:show}" v-if="page===5">
            <div class="game_area">
                <div class="data">
                    <div class="score_area">10</div>
                    <div class="time_area">60s</div>
                </div>
                <!-- 郎中 -->
                <img src="img/people1.png" class="people">
                <!-- 仕女 -->
                <img src="img/people2.png" class="people">
                <!-- 商贾 -->
                <img src="img/people3.png" class="people">
                <!-- 文人 -->
                <img src="img/people4.png" class="people">
                <!-- 对话框 -->
                <div class="talk">
                    连日苦读，暑气攻心，劳烦掌柜赐一盏清润解暑，带些山野之气的冰饮，可好？
                </div>
                <!-- 云纹玫瑰囊 -->
                <img src="img/spirit1.png" class="spirit1"> 
                <!-- 薄荷香缨囊 -->
                <img src="img/spirit2.png" class="spirit2">
                <!-- 玫瑰缠花酥 -->
                <img src="img/spirit3.png" class="spirit3">
                <!-- 玉斛冰泉饮 -->
                <img src="img/spirit4.png" class="spirit4">
                <!-- 玫瑰冰酪 -->
                <img src="img/spirit5.png" class="spirit5">
                <!-- 盘子 -->
                <div class="panzi">

                </div>
                <!-- 交付按钮 -->
                <img src="img/jf.png" class="jf">
            </div>
        </div>
        <!-- 提交成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <img src="img/button4_2.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 无机会弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===2">
                <div class="popup popup2">
                    <img src="img/close.png" class="close2" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 游戏成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===3">
                <div class="popup popup3">
                    <img src="img/button8.png" class="back" @click="getPrize">
                </div>
            </div>
        </transition>
        <!-- 游戏失败弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===4">
                <div class="popup popup4">
                    <img src="img/button9.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===5">
                <div class="popup popup5">
                    <div class="p3" v-html="prizeData.ad"></div>
                    <div class="p4">{{prizeData.prize}}</div>
                    <img src="img/button10.png" class="back" @click="goForm">
                </div>
            </div>
        </transition>
        <!-- 未中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===6">
                <div class="popup popup6">
                    <img src="img/button11.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
    </div>
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.js"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
    </script>
    <script>
        let rotation = [];
        window.startData = {
            rotation:rotation,
            jihui: '5',
            prize: '扑克牌一份',
            ad: '云水雅玩礼',
            prizeTime: '{$prizeTime}',
            userInfo: {
                name: '{$name}',
                phone: '{$phone}',
                area: '{$area}',
                address: '{$address}',
            },
            nickname: '{$nickname}',
            avatar: '{$headimgurl}',
            wlq:'{$wlq}',
            endtime: '{$endtime}',
            writeFlag: '{$writeFlag}',
            rule: `活动时间：2025年6月27日—6月29日<br>
            活动规则：<br>
            1、点击开始进入游戏，于界面下方触发客户需求浮窗，点击茶饮（玉斛冰泉饮 / 玫瑰冰酪）、点心（玫瑰缠花酥）、香囊（薄荷香缨囊 / 云纹玫瑰囊）置入【结算区】，匹配顾客需求，点击“交付”按钮视为确认。正确交付加10-20经验值，错误匹配扣 5-10经验值。在 60s 内累计经验值100即为挑战成功，获得 1次抽奖机会。倒计时结束未达到100分，即视作挑战失败。<br>
            2、游戏过程中，不可重复选取同品类商品，点击【结算区】的商品即可撤回。<br>
            3、每位用户每天有5次游戏机会，中途退出消耗1 次机会。<br>
            4、在挑战成功的用户中随机抽选200名，派送礼品一份，奖品多多，快来参与~<br>
            5、用户在填写信息时， 需填写详细联系方式（姓名、电话号码和邮寄地址），因个人原因（包括但不限于地址电话填错、电话无法接通、地址填写不完整等）导致快递退回，均不予补发处理。<br>
            6、在同一次活动中，姓名、手机号、收货地址中有一项及以上相同时，默认只寄发首次中奖奖品。物流相关事宜（包括签收、破损、丢件等问题），由中奖者自行和快递公司协商处理。<br>
            7、活动过程中有任何问题可发送疑问至“甜润世界”微信公众号后台。`,
        }

        const app = createApp({
            setup() {
                const { on, bgClick } = useBgMusic('123.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const { userInfo, endtime, writeFlag } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const handleRotation = startData.rotation.map(item => item.split(',')) // 设置顶部获奖数据
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    if (opportunity.value >= 1) {
                        page.value = 5
                        opportunity.value--
                        defaultHttp('gamestart', { status: 1 })
                    } else {
                        show.value = 2
                    }
                }

                const edit = () => {
                    if (writeFlag === 0) {
                        return vantAlert('活动已结束');
                    } else {
                        goForm()
                    }
                }
                const goForm = () => {
                    page.value = 4
                    show.value = 0
                }

                // 登记信息功能
                const { form, popupShow, options, focus, onConfirm, check, selectedValues } = createdForm()
                form.value = userInfo
                const submit = throttle(async () => {
                    if (!check()) return
                    const res = await defaultHttp('action', Object.assign({ act: 'sub' }, form.value), { status: 1 })
                    if (res.status == 1) {
                        show.value = 1
                    } else {
                        vantAlert(res.msg)
                    }
                })
                // 刷新页面功能
                const { reload, savePage } = useReload()
                if (savePage) { page.value = +savePage }
                // 判断是否是初次进入网页,执行预加载
                const { loadStart, progress, progressShow, startFlag } = cheackStartPopup([])
                loadStart()
                if(startFlag){
                    page.value = 2
                }
                // 检查未领取
                if (startData.wlq === '1') {
                    vant.showConfirmDialog({
                        title: '温馨提示',
                        message: '您已中奖，请尽快完善领奖信息！',
                        confirmButtonText: '去领取',
                        cancelButtonText: '不领取'
                    }).then(() => {
                        show.value = 0
                        page.value = 4
                    })
                }

                const gameEnd = throttle(async (e) => {
                    const res = await defaultHttp('gameEnd', { cg:e?1:0, timer: 60, score:100 }, { status: 1 })
                    if (res.status === 1) {
                        show.value = 3
                    } else if(res.status === 2) {
                        show.value = 4
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 抽奖逻辑
                const prizeData = ref({ prize: startData.prize, ad: startData.ad, prizeType: 0 })
                const getPrize = throttle(async () => {
                    const res = await defaultHttp('getprize', {}, { status: 1, msg: '抽中奖品', data: { prize: '扑克牌一份', ad: '云水雅玩礼', prizeType: 1 } })
                    if (res.status === 1) {
                        show.value = 5
                        res.data.ad = res.data.ad.replaceAll("\\n", "<br>")
                        prizeData.value = res.data
                    } else if (res.status === 2) {
                        show.value = 6 //未中奖
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })
                // 游戏逻辑
                return {
                    startData, page, show,
                    handleRotation, edit, goForm,
                    on, bgClick,
                    start, submit, reload,
                    form, popupShow, options, focus, onConfirm, check, selectedValues,
                    prizeData, getPrize,
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
</body>

</html>



