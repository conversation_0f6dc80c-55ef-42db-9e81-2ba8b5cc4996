@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
  font-family: 'TBMCYXT';
  src: url(https://ztimg.hefei.cc/static/common/fonts/TaoBaoMaiCaiTi-Regular.ttf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 2vh;
}
.musicbtn {
  width: 6vh;
  height: 6vh;
  top: 2vh;
  right: 2vh;
  background-image: url(../img/music.png);
  z-index: 11;
}
.logo {
  width: 14vh;
  position: absolute;
  top: 2vh;
  left: 2vh;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 102vh;
  height: 100vh;
  width: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
  background: linear-gradient(180deg, #73cda9 0%, #f4c47a 100%);
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #215444;
  background: url(../img/bj.jpg) no-repeat center center / 60vh auto;
}
.warp .page .rotation {
  margin-top: -12vh;
  width: 39vh;
  height: 5vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 3;
}
.warp .page .rotation .van-swipe {
  height: 5vh;
}
.warp .page .rotation .van-swipe .van-swipe-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.warp .page .rotation p {
  text-align: center;
  white-space: nowrap;
  font-size: 2vh;
}
.warp .page .rotation p span {
  font-size: 2vh;
}
.warp .page .title {
  width: 55vh;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: -1vh;
  width: 38vh;
}
.warp .page .title3 {
  width: 38vh;
  position: absolute;
  top: 13vh;
}
.warp .page .tip {
  font-size: 2vh;
  position: absolute;
  bottom: 12vh;
  right: 4vh;
  color: #fff;
  transform: scale(0.5);
  transform-origin: 100% 50%;
  white-space: nowrap;
  letter-spacing: 1px;
}
.warp .page .arrow {
  position: absolute;
  top: 50%;
  left: 3vh;
}
.warp .page .arrow:before {
  content: '';
  display: inline-block;
  width: 7vh;
  height: 7vh;
  background: url(../img/arrow.png) no-repeat center center / 100% 100%;
  transform: rotate(180deg);
}
.warp .page .arrow_right {
  position: absolute;
  top: 50%;
  right: 3vh;
}
.warp .page .arrow_right:before {
  content: '';
  display: inline-block;
  width: 7vh;
  height: 7vh;
  background: url(../img/arrow.png) no-repeat center center / 100% 100%;
}
.warp .page .start {
  margin-top: 20vh;
  width: 19vh;
  height: 19vh;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .bg2 {
  width: 60vh;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .bg3 {
  width: 60vh;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: absolute;
  z-index: 2;
  bottom: 4vh;
  display: flex;
  justify-content: center;
}
.warp .page .button_container .button {
  height: 6vh;
  margin: 0 5vh;
}
.warp .page .game_area {
  margin-top: 12vh;
  width: 58vh;
  height: 81vh;
  padding: 0 3vh;
  background: url(../img/game_area.png) no-repeat center center / 100% 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.warp .page .game_area canvas {
  width: 100%;
  height: 100%;
}
.warp .page .game_area .data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 0;
  width: 100%;
  position: absolute;
  top: -9vh;
}
.warp .page .game_area .data .time {
  width: 8vh;
  height: 8vh;
  border: 1px solid #fff;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  line-height: 1;
  position: absolute;
  left: 4vh;
}
.warp .page .game_area .data .time span:nth-last-child(1) {
  font-weight: bold;
  font-size: 2vh;
}
.warp .page .game_area .data .score {
  width: 15vh;
  height: 10vh;
  background: url(../img/score.png) no-repeat center center / 100% 100%;
  color: #DBEFB4;
  font-size: 4vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 2vh;
}
.warp .page .poster_area {
  width: 60vh;
  height: 100vh;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}
.warp .page .poster_area .poster {
  margin-top: calc((100vh - 128vh) / 2);
  width: 60vh;
  height: 128vh;
  flex-shrink: 0;
}
.warp .page .poster_area .poster_p1 {
  position: absolute;
  top: 16vh;
  left: 12vh;
  text-align: center;
  color: #000;
  font-weight: bold;
  font-size: 2vh;
}
.warp .page .poster_area .avatar {
  width: 8vh;
  height: 8vh;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 14vh;
  left: 2vh;
}
.warp .page .button_area {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 5vh;
}
.warp .page .button_area .button11 {
  height: 6vh;
  margin: 0 2vh;
}
.warp .bj2 {
  background-image: url(../img/bj2.jpg);
}
.warp .bj3 {
  background-image: url(../img/bj3.jpg);
}
.warp .bj4 {
  background-image: url(../img/bj4.jpg);
}
.warp .bj5 {
  background-image: url(../img/bj5.jpg);
}
.warp .bj6 {
  background-image: url(../img/bj6.jpg);
}
.blur {
  filter: blur(1vh);
}
.fc {
  justify-content: center;
}
.area {
  width: 59vh;
  height: 65vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/area.png) no-repeat center center / 100% 100%;
}
.area .stit {
  position: absolute;
  top: -1vh;
  width: 24vh;
}
.area .spirit1 {
  position: absolute;
  width: 18vh;
  left: -5vh;
  bottom: -4vh;
}
.area .back {
  position: absolute;
  bottom: -4vh;
  width: 24vh;
}
.area .submit {
  position: absolute;
  bottom: -9vh;
  width: 24vh;
}
.area .rule {
  width: 100%;
  padding: 0 4vh;
  margin: 6vh 0 6vh;
  flex: 1;
  overflow-y: auto;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: -0vh;
  position: relative;
}
.area .prize {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 2vh;
}
.area .prize .mt5 {
  margin-top: 1vh;
}
.area .prize .info {
  padding: 6vh 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 10vh;
  width: 42vh;
}
.area .prize .info:first-child {
  border-bottom: 1px dashed #215444;
}
.area .prize .info .p2 {
  font-size: 3vh;
  line-height: 4vh;
  max-width: 45vh;
  text-align: center;
}
.area .prize .info .jptit {
  width: 18vh;
  margin-bottom: 1vh;
}
.area .prize .edit {
  margin-top: 2vh;
  width: 24vh;
}
.area .form {
  width: 100%;
  padding: 10vh 3vh 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item {
  margin-left: 0;
  margin-bottom: 5vh;
  display: flex;
  align-items: center;
}
.area .form .form-item label {
  width: 16vh;
  font-weight: 500;
  font-size: 3vh;
  white-space: nowrap;
  color: #215444;
  flex-shrink: 0;
}
.area .form .form-item div input {
  margin-bottom: 2vh;
}
.area .form .form-item div input:nth-last-child(1) {
  margin-bottom: 0;
}
.area .form .form-item .right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item input {
  margin-left: 0vh;
  padding-left: 2vh;
  width: 30vh;
  height: 5vh;
  border: 1px #215444 solid;
  flex-shrink: 0;
  opacity: 1;
  color: #215444;
  font-size: 3vh;
}
.area .form .form-item input::-webkit-input-placeholder {
  color: #215444;
  opacity: 0.6;
}
.area .form .form-item input:-moz-placeholder {
  color: #215444;
  opacity: 0.6;
}
.area .form .form-item input::-moz-placeholder {
  color: #215444;
  opacity: 0.6;
}
.area .form .form-item input:-ms-input-placeholder {
  color: #215444;
  opacity: 0.6;
}
.area .form .form-item #getArea {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.area .form .form-footer {
  margin-top: -6vh;
  display: flex;
  width: 200%;
  transform: scale(0.5);
  color: #215444;
}
.area .form .form-footer .fz1 {
  font-size: 5vh;
}
.area .form .form-footer p {
  font-size: 4vh;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: 0vh;
}
.area .form .button {
  margin-top: -3vh;
  width: 18vh;
}
.area .form .fs {
  align-items: flex-start;
}
.area .form .fs label {
  margin-top: 0vh;
}
.area2 {
  background: url(../img/area2.png) no-repeat center center / 100% 100%;
  width: 53vh;
  height: 84vh;
}
.area2 .stit {
  top: -3vh;
}
.button5 {
  margin-top: -4vh;
  width: 24vh;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #2b7952;
  font-family: 'TBMCYXT';
  font-weight: 300;
}
.mask .popup {
  margin-top: -1vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .p2 {
  font-size: 3vh;
  margin-top: -12vh;
}
.mask .popup .back {
  width: 30vh;
  position: absolute;
  bottom: -4vh;
}
.mask .showpop1 {
  width: 54vh;
  height: 92vh;
  background: url(../img/showpop1.png) no-repeat center top / 100% 100%;
}
.mask .showpop1 .back {
  width: 24vh;
  position: absolute;
  bottom: 3vh;
}
.mask .showpop2 {
  width: 54vh;
  height: 92vh;
  background: url(../img/showpop2.png) no-repeat center top / 100% 100%;
}
.mask .showpop2 .back {
  width: 24vh;
  position: absolute;
  bottom: 3vh;
}
.mask .popup1 {
  width: 53vh;
  height: 60vh;
  background: url(../img/popup1.png) no-repeat center top / 100% auto;
}
.mask .popup2 {
  width: 54vh;
  height: 24vh;
  background: url(../img/popup2.png) no-repeat center top / 100% auto;
}
.mask .popup3 {
  width: 53vh;
  height: 60vh;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup4 {
  width: 53vh;
  height: 60vh;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup5 {
  width: 53vh;
  height: 60vh;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
  padding-top: 7vh;
}
.mask .popup5 .p3 {
  margin-top: -2vh;
  font-size: 8vh;
  white-space: nowrap;
}
.mask .popup5 .p4 {
  margin-top: 4vh;
  font-size: 6vh;
  white-space: nowrap;
}
.mask .popup6 {
  width: 53vh;
  height: 60vh;
  background: url(../img/popup6.png) no-repeat center top / 100% 100%;
}
.mask .poster {
  width: 60vh;
  animation: sc 0.5s ease-in-out forwards;
  animation-delay: 0.5s;
}
.mask .white {
  width: 60vh;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  background-color: white;
  /* 设置为你想要的背景颜色 */
  animation: flash 0.5s ease-in-out forwards;
  /* 定义动画名称、时长和缓动函数 */
  pointer-events: none;
}
.mask .poster_tip {
  color: #fff;
  position: absolute;
  bottom: 16vh;
  animation: flash2 1s ease-in-out forwards;
}
.mask .close {
  width: 5vh;
  height: 5vh;
  background: url(../img/close.png?v=1) no-repeat center center / 100% 100%;
  position: absolute;
  bottom: 6vh;
  animation: flash2 1s ease-in-out forwards;
}
.blink-2 {
  animation: blink-2 1s linear infinite both;
}
@keyframes blink-2 {
  0%,
  10%,
  90%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.2;
  }
}
@keyframes flash {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes flash2 {
  0%,
  99% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes sc {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.7) translateY(-6vh);
  }
}
