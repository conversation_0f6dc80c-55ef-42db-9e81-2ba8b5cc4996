// 取消微信浏览器点击的蓝色背景
// *{
//     -webkit-touch-callout:none;
//     -webkit-user-select:none; 
//     -moz-user-select:none;
//     -ms-user-select:none;
//     user-select:none;
//     -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
//     -webkit-user-select: none;
//     -moz-user-focus: none;
//     -moz-user-select: none
// }

@font-face {
    font-family: '思源宋体';
    src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}

// @font-face {
//     font-family: 'TBMCYXT';
//     src: url(https://ztimg.hefei.cc/static/common/fonts/TaoBaoMaiCaiTi-Regular.ttf);
// }

body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
    font-size: 3.5vw
}

@formcolor: #352219;


.musicbtn {
    width: 9.6vw;
    height: 9.6vw;
    top: 3.6vw;
    right: 2.8vw;
    background-image: url(../img/music.png);
    z-index: 11;
}

.warp {
    margin: 0 auto;
    min-height: 170vw;
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: '思源宋体';
    background: linear-gradient(180deg, #fdf9ec 0%, #e3e3e3 100%);

    .swipe_container {
        width: 100%;
        height: 100%;
    }

    .page {
        width: 100%;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        color: #352219;
        background: url(../img/bj.jpg) no-repeat center center/100vw auto;

        .rotation {
            width: 65vw;
            height: 8.8vw;
            position: absolute;
            top: 10vw;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 3;

            .van-swipe {
                height: 8.8vw;

                .van-swipe-item {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                }
            }

            p {
                text-align: center;
                white-space: nowrap;
                font-size: 4vw;

                span {
                    font-size: 4vw;
                }
            }
        }

        .title {
            margin-top: 20vw;
            width: 79.2vw;
            z-index: 2;
        }

        .title2 {
            margin-top: 3vw;
            width: 60vw;
            z-index: 3;
        }

        .start {
            position: absolute;
            bottom: 10vh;
            width: 17.7333vw;
            height: 34.9333vw;
            flex-shrink: 0;
            background: url(../img/start.png) no-repeat center center/100% 100%;
            z-index: 2;
        }

        .bg2 {
            width: 100vw;
            position: absolute;
            left: 0;
            bottom: 0;
            pointer-events: none;
        }

        .bg3 {
            width: 100vw;
            position: absolute;
            left: 0;
            top: 0;
            pointer-events: none;
        }

        .button_container {
            position: relative;
            z-index: 2;
            margin-top: 50vw;
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0 1vw;

            .button {
                height: 40vw;
            }
        }

        .tip {
            margin-top: 110vw;
            width: 65.0667vw;
            height: 42.8vw;
            background: url(../img/tip.png) no-repeat center center/100% 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;

            .button5 {
                width: 42.1333vw;
                position: absolute;
                bottom: -5vw;
            }
        }

        .game_area {
            width: 100vw;
            height: 177.8667vw;
            position: relative;
            .data {
                position: absolute;
                top: 16.6667vw;
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 8vw;
                .score_area {
                    width: 19.7333vw;
                    height: 21.0667vw;
                    background: url(../img/score_area.png) no-repeat center center/100% 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 6vw;
                    font-weight: bold;
                    padding-bottom: 1vw;
                }

                .time_area {
                    width: 19.7333vw;
                    height: 21.0667vw;
                    background: url(../img/time_area.png) no-repeat center center/100% 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 6vw;
                    font-weight: bold;
                    padding-bottom: 1vw;
                }
            }
            .spirit1 {
                width: 9.4667vw;
                position: absolute;
                top: 76vw;
                left: 65vw;
            }

            .spirit2 {
                width: 10.8vw;
                position: absolute;
                top: 72vw;
                left: 84vw;
            }

            .spirit3 {
                width: 11.8667vw;
                position: absolute;
                top: 101vw;
                left: 53vw;
            }

            .spirit4 {
                width: 6.6667vw;
                position: absolute;
                top: 97vw;
                left: 72vw;
            }

            .spirit5 {
                width: 7.8667vw;
                position: absolute;
                top: 100vw;
                left: 88vw;
            }

            .panzi {
                width: 37vw;
                height: 16vw;
                background-color: rgba(255, 255, 255, 0.6);
                position: absolute;
                top: 129vw;
                left: 48vw;
            }

            .jf {
                width: 16.8vw;
                position: absolute;
                bottom: 47vw;
                right: 24vw;
            }

            .people {
                height: 76.8vw;
                position: absolute;
                right: 0;
                bottom: -19vw;
                pointer-events: none;
            }
            .talk{
                width: 67vw;
                min-height: 14.4vw;
                border-radius: 1vw;
                background-color: #f9f1e6;
                padding: 2vw 1vw;
                color: #000;
                border: 1px solid #7b6352;
                position: absolute;
                top: 145vw;
                left: 3vw;
            }
        }
    }

    .bj2 {
        background-image: url(../img/bj2.jpg);
    }

    .bj3 {
        background-image: url(../img/bj3.jpg);
    }
}

.blur {
    filter: blur(1vw);
}

.fc {
    justify-content: center;
}

.area {
    margin-top: -8vw;
    width: 93.6vw;
    height: 126.8vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: url(../img/area.png) no-repeat center center/100% 100%;

    .stit {
        margin-top: 10vw;
        width: 46vw;
    }

    .spirit1 {
        position: absolute;
        width: 30.4vw;
        left: -9vw;
        bottom: -6vw;
    }

    .back {
        position: absolute;
        bottom: -4vw;
        width: 45.3333vw;
    }

    .submit {
        position: absolute;
        bottom: -15vw;
        width: 40vw;
    }

    .rule {
        width: 100%;
        padding: 0 4vw;
        margin: 2vw 0 10vw;
        flex: 1;
        overflow-y: auto;
        line-height: 1.5;
        text-align: justify;
        letter-spacing: -0.1vw;
        position: relative;
    }

    .prize {
        display: flex;
        flex-direction: column;
        align-items: center;

        .mt5 {
            margin-top: 2vw;
        }

        .info {
            padding: 5vw 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 16vw;
            width: 70vw;

            &:first-child {
                border-bottom: 1px dashed #215444;
            }

            .p2 {
                font-size: 5vw;
                line-height: 7vw;
                max-width: 75vw;
                text-align: center;
            }

            .jptit {
                width: 29.467vw;
                margin-bottom: 2vw;
            }
        }

        .edit {
            width: 45.3333vw;
        }
    }


    .form {
        width: 100%;
        padding: 16vw 5vw 0;
        display: flex;
        flex-direction: column;
        align-items: center;

        .form-item {
            margin-left: 0;
            margin-bottom: 5vw;
            display: flex;
            align-items: center;

            label {
                width: 22vw;
                font-weight: bold;
                font-size: 4.6vw;
                white-space: nowrap;
                color: @formcolor;
                flex-shrink: 0;
            }

            div {

                input {
                    margin-bottom: 3vw;

                }

                input:nth-last-child(1) {
                    margin-bottom: 0;
                }
            }

            .right {
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            input {
                margin-left: 0.8vw;
                padding-left: 2.5333vw;
                width: 50vw;
                height: 7.7333vw;
                border: 1px @formcolor solid;
                flex-shrink: 0;
                // border-radius: 0.8vw;
                opacity: 1;
                color: @formcolor;
                font-size: 4.6vw;

                &::-webkit-input-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &:-moz-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &::-moz-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }

                &:-ms-input-placeholder {
                    color: @formcolor;
                    opacity: 0.6;
                }
            }

            #getArea {
                opacity: 0;
                position: absolute;
                z-index: -1;
            }

        }

        .form-footer {
            margin-top: -10vw;
            display: flex;
            width: 200%;
            transform: scale(0.5);
            color: @formcolor;

            .fz1 {
                font-size: 6vw;
            }

            p {
                font-size: 6vw;
                line-height: 1.5;
                text-align: justify;
                letter-spacing: 0.3vw;
            }
        }

        .button {
            margin-top: -5vw;
            width: 30.4vw;
        }

        .fs {
            align-items: flex-start;

            label {
                margin-top: 0.5vw;
            }
        }
    }
}

.area2 {
    margin-top: 0;
    width: 87.2vw;
    height: 136.5333vw;
    background: url(../img/area2.png) no-repeat center center/100% 100%;

    .stit {
        margin-top: -21vw;
        margin-bottom: 10vw;
    }

    .back {
        bottom: 8vw;
    }
}

.mask {
    z-index: 10;
    position: fixed;
    top: 0;
    left: 0;
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(18, 45, 29, 0.3);
    // backdrop-filter: blur(4px);
    transform: translateX(-50%);
    left: 50%;
    color: #352219;
    font-weight: 300;

    .popup {
        margin-top: -1vw;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;

        .back {
            width: 42.6667vw;
            position: absolute;
            bottom: -6vw;
        }

    }

    .popup1 {
        width: 87.8667vw;
        height: 68.2667vw;
        background: url(../img/popup1.png) no-repeat center top / 100% 100%;
    }

    .popup2 {
        width: 75.4667vw;
        height: 64.8vw;
        background: url(../img/popup2.png) no-repeat center top / 100% 100%;

        .close2 {
            width: 10.8vw;
            position: absolute;
            top: 0;
            right: -2vw;
        }
    }

    .popup3 {
        width: 75.4667vw;
        height: 60.8vw;
        background: url(../img/popup3.png) no-repeat center top / 100% 100%;
        .back{
            bottom: 9vw;
        }
    }

    .popup4 {
        width: 75.4667vw;
        height: 60.8vw;
        background: url(../img/popup4.png) no-repeat center top / 100% 100%;
        .back{
            bottom: 9vw;
        }
    }

    .popup5 {
        width: 75.4667vw;
        height: 66.1333vw;
        background: url(../img/popup5.png) no-repeat center top / 100% 100%;
        padding-top: 0vw;

        .p3 {
            margin-top: -4vw;
            font-size: 7vw;
            white-space: nowrap;
        }

        .p4 {
            font-size: 7vw;
            white-space: nowrap;
        }
        .back{
            bottom: 9vw;
        }
    }

    .popup6 {
        width: 75.4667vw;
        height: 66.1333vw;
        background: url(../img/popup6.png) no-repeat center top / 100% 100%;
        .back{
            bottom: 9vw;
        }
    }
}